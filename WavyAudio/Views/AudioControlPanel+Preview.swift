import SwiftUI
import AVFoundation

struct AudioControlPanel_Previews: PreviewProvider {
    static var previews: some View {
        let audioEngine = PreviewAudioEngine()
        let deviceManager = PreviewDeviceManager()
        
        AudioControlPanel()
            .environmentObject(audioEngine)
            .environmentObject(deviceManager)
            .frame(width: 400, height: 500)
            .padding()
    }
}

// MARK: - Preview Helpers

// Mock AudioEngineManager for previews
class PreviewAudioEngine: AudioEngineManager {
    // Override properties for preview
    var currentLatency: Double = 0.005 // 5ms
    var cpuUsage: Double = 35.7
    var bufferSize: Int = 256
    
    override init() {
        super.init()
        // Initialize preview-specific state
    }
}

// Mock DeviceManager for previews
class PreviewDeviceManager: DeviceManager {
    override init() {
        super.init()
        // Add some mock devices for preview
        self.devices = [
            AudioDevice(
                id: UUID(),
                deviceID: 1,
                name: "Built-in Output",
                manufacturer: "Apple Inc.",
                inputChannels: 0,
                outputChannels: 2,
                sampleRate: 44100.0,
                isVirtual: false,
                isAggregate: false
            ),
            AudioDevice(
                id: UUID(),
                deviceID: 2,
                name: "External Headphones",
                manufacturer: "Acme Corp",
                inputChannels: 0,
                outputChannels: 2,
                sampleRate: 44100.0,
                isVirtual: false,
                isAggregate: false
            )
        ]
    }
}
